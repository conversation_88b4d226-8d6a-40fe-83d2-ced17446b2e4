<script setup lang="ts">
import { EmbedDirectTemplate } from '@documenso/embed-vue'
import { ref, onMounted, nextTick, watch } from 'vue'

// Define emits
const emit = defineEmits(['documentCompleted'])

// Props
const props = defineProps({
  token: {
    type: String,
    required: true
  },
  signerName: {
    type: String,
    default: ''
  },
  signerEmail: {
    type: String,
    default: ''
  },
  externalId: {
    type: String,
    default: ''
  }
})

// State for debugging
const documentReady = ref<boolean>(false)
const documentError = ref<string>('')
const documentCompleted = ref<boolean>(false)

console.log('=== DOCUMENSO EMBED DEBUG ===')
console.log('Token:', props.token)
console.log('Token length:', props.token?.length)
console.log('Signer name:', props.signerName)
console.log('Signer email:', props.signerEmail)
console.log('External ID:', props.externalId)
console.log('Environment:', typeof window !== 'undefined' ? 'Client' : 'Server')
console.log('Current URL:', typeof window !== 'undefined' ? window.location.href : 'Server-side')
console.log('Expected Documenso URL:', `https://app.documenso.com/embed/direct/${props.token}`)

// Event handlers
const onDocumentReady = () => {
  console.log('✅ Document ready for signing')
  documentReady.value = true
  documentError.value = ''

  // Force iframe to proper size after document is ready
  setTimeout(() => forceIframeSize(), 100)
  setTimeout(() => forceIframeSize(), 500)
}

const onDocumentCompleted = (data: any) => {
  console.log('🎉🎉🎉 DOCUMENSO COMPONENT: Document completed!', data)
  console.log('🎉🎉🎉 DOCUMENSO COMPONENT: About to emit documentCompleted event')
  documentCompleted.value = true

  // Emit event to parent component
  emit('documentCompleted', data)
  console.log('🎉🎉🎉 DOCUMENSO COMPONENT: Event emitted successfully!')
}

const onDocumentError = (error: any) => {
  console.error('❌ Document error:', error)
  documentError.value = error.message || 'Unknown error occurred'
}

const onFieldSigned = (field: any) => {
  console.log('✍️ Field signed:', field)
}

const onFieldUnsigned = (field: any) => {
  console.log('↩️ Field unsigned:', field)
}

// Generic event handler to catch any completion-related events
const onGenericComplete = (data: any) => {
  console.log('🎉🎉🎉 GENERIC COMPLETE EVENT CAUGHT:', data)
  onDocumentCompleted(data)
}

// Watch for changes in documentCompleted state as a fallback
watch(documentCompleted, (newValue, oldValue) => {
  if (newValue && !oldValue) {
    console.log('🎉🎉🎉 WATCHER: documentCompleted changed to true!')
    emit('documentCompleted', { source: 'watcher', timestamp: new Date().toISOString() })
  }
})

// Polling function to detect document completion
const startCompletionPolling = () => {
  console.log('🔍 Starting completion polling...')

  const pollInterval = setInterval(() => {
    try {
      // Look for completion indicators in the DOM
      const iframe = document.querySelector('.documenso-embed iframe') as HTMLIFrameElement

      // Check for success messages or completion indicators
      const successElements = document.querySelectorAll('[data-testid*="success"], [data-testid*="complete"], .success, .completed')
      const completionText = document.body.innerText.toLowerCase()

      // Look for text indicators of completion - specifically the Documenso completion message
      const completionIndicators = [
        'document completed!',
        'document completed',
        'successfully signed',
        'signing complete',
        'document signed',
        'all fields completed',
        'thank you'
      ]

      // Also specifically check for the h3 element with "Document Completed!"
      const completionHeaders = document.querySelectorAll('h3')
      let hasCompletionHeader = false
      completionHeaders.forEach(header => {
        if (header.textContent && header.textContent.toLowerCase().includes('document completed')) {
          hasCompletionHeader = true
          console.log('🎉🎉🎉 POLLING: Found completion header:', header.textContent)
        }
      })

      const hasCompletionText = completionIndicators.some(indicator =>
        completionText.includes(indicator)
      )

      if (successElements.length > 0 || hasCompletionText || hasCompletionHeader) {
        console.log('🎉🎉🎉 POLLING: Document completion detected!')
        console.log('🎉🎉🎉 POLLING: Success elements found:', successElements.length)
        console.log('🎉🎉🎉 POLLING: Completion text detected:', hasCompletionText)
        console.log('🎉🎉🎉 POLLING: Completion header detected:', hasCompletionHeader)

        clearInterval(pollInterval)
        documentCompleted.value = true
        onDocumentCompleted({ source: 'polling', timestamp: new Date().toISOString() })
      }

      // Also check iframe content if accessible
      if (iframe && iframe.contentDocument) {
        try {
          const iframeText = iframe.contentDocument.body.innerText.toLowerCase()
          const iframeHeaders = iframe.contentDocument.querySelectorAll('h3')

          let iframeHasCompletionHeader = false
          iframeHeaders.forEach(header => {
            if (header.textContent && header.textContent.toLowerCase().includes('document completed')) {
              iframeHasCompletionHeader = true
              console.log('🎉🎉🎉 POLLING: Found completion header in iframe:', header.textContent)
            }
          })

          const iframeHasCompletion = completionIndicators.some(indicator =>
            iframeText.includes(indicator)
          ) || iframeHasCompletionHeader

          if (iframeHasCompletion) {
            console.log('🎉🎉🎉 POLLING: Completion detected in iframe!')
            clearInterval(pollInterval)
            documentCompleted.value = true
            onDocumentCompleted({ source: 'iframe-polling', timestamp: new Date().toISOString() })
          }
        } catch (e) {
          // Iframe content not accessible due to CORS
          console.log('🔍 POLLING: Iframe content not accessible (CORS)')
        }
      }

    } catch (error) {
      console.log('Polling error (non-critical):', error)
    }
  }, 1000) // Check every 1 second for faster detection

  // Stop polling after 5 minutes to prevent infinite polling
  setTimeout(() => {
    clearInterval(pollInterval)
    console.log('🔍 Completion polling stopped after timeout')
  }, 300000)
}

// Custom CSS to make the embed fill the width like the PDF viewer
const customCSS = `
  .documenso-embed {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 800px !important;
    height: 1200px !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    background: white !important;
  }

  .documenso-embed iframe,
  .documenso-embed > div {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 1000px !important;
    height: 1200px !important;
  }

  /* Ensure the document viewer takes full width */
  [data-testid="document-viewer"],
  .document-viewer {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Force white backgrounds using Documenso component classes */
  .embed--Root,
  .embed--DocumentContainer,
  .embed--DocumentViewer,
  .embed--DocumentWidget,
  .embed--DocumentWidgetContainer,
  .embed--DocumentWidgetHeader,
  .embed--DocumentWidgetContent,
  .embed--DocumentWidgetForm,
  .embed--DocumentWidgetFooter {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
  }

  /* Force white background on body and html inside iframe */
  body, html {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
  }

  /* AGGRESSIVELY OVERRIDE ALL GREEN COLORS WITH BLUE */
  * {
    --success: #71AEEE !important;
    --success-foreground: #FFFFFF !important;
    --primary: #71AEEE !important;
    --primary-foreground: #FFFFFF !important;
    --accent: #71AEEE !important;
    --accent-foreground: #FFFFFF !important;
  }

  /* Force blue color for buttons and interactive elements */
  button[type="submit"],
  button[data-testid*="sign"],
  button[data-testid*="complete"],
  button[data-testid*="finish"],
  .btn-primary,
  .btn-success,
  [role="button"]:not([data-testid*="cancel"]):not([data-testid*="back"]),
  .bg-green-500,
  .bg-green-600,
  .bg-emerald-500,
  .bg-emerald-600 {
    background-color: #71AEEE !important;
    border-color: #71AEEE !important;
    color: #FFFFFF !important;
  }

  /* Force blue color for checkboxes and form controls */
  input[type="checkbox"]:checked,
  input[type="radio"]:checked,
  .checkbox-checked,
  .form-control:focus,
  .form-control:checked {
    background-color: #71AEEE !important;
    border-color: #71AEEE !important;
    accent-color: #71AEEE !important;
  }

  /* OVERRIDE ANY GREEN SUCCESS COLORS */
  .text-green-500,
  .text-green-600,
  .text-emerald-500,
  .text-emerald-600,
  .text-success,
  [class*="text-green"],
  [class*="bg-green"],
  [class*="border-green"],
  [style*="color: green"],
  [style*="background-color: green"],
  [style*="border-color: green"] {
    color: #71AEEE !important;
    background-color: #71AEEE !important;
    border-color: #71AEEE !important;
  }

  /* Force blue color for signature fields and active states */
  [data-field-type="SIGNATURE"][data-inserted="true"],
  [data-field-type="CHECKBOX"][data-inserted="true"],
  [data-field-type="RADIO"][data-inserted="true"],
  .signature-field.completed,
  .field-completed,
  .field--FieldRootContainer[data-field-type="SIGNATURE"],
  .field--FieldRootContainer[data-field-type="SIGNATURE"][data-inserted="true"],
  .field--FieldRootContainer[data-field-type="CHECKBOX"],
  .field--FieldRootContainer[data-field-type="CHECKBOX"][data-inserted="true"] {
    border-color: #71AEEE !important;
    background-color: rgba(113, 174, 238, 0.1) !important;
  }

  /* Specific targeting for signature and checkbox fields */
  .field--FieldRootContainer[data-field-type="SIGNATURE"] {
    border: 2px solid #71AEEE !important;
    background-color: rgba(113, 174, 238, 0.05) !important;
  }

  .field--FieldRootContainer[data-field-type="SIGNATURE"][data-inserted="true"] {
    border: 2px solid #71AEEE !important;
    background-color: rgba(113, 174, 238, 0.2) !important;
  }

  .field--FieldRootContainer[data-field-type="CHECKBOX"] {
    border: 2px solid #71AEEE !important;
  }

  .field--FieldRootContainer[data-field-type="CHECKBOX"][data-inserted="true"] {
    background-color: #71AEEE !important;
    border: 2px solid #71AEEE !important;
  }
`

// CSS Variables for additional customization - FORCE BLUE EVERYWHERE
const cssVariables = {
  // Colors - Force white backgrounds everywhere
  background: '#FFFFFF',
  foreground: '#000000',
  muted: '#FFFFFF',
  mutedForeground: '#6B7280',
  popover: '#FFFFFF',
  popoverForeground: '#000000',
  card: '#FFFFFF',
  cardBorder: '#71AEEE', // Blue borders
  cardForeground: '#000000',
  fieldCard: '#FFFFFF',
  fieldCardBorder: '#71AEEE', // Blue borders for fields
  fieldCardForeground: '#000000',
  widget: '#FFFFFF',
  widgetForeground: '#000000',

  // Primary colors - FORCE BLUE
  primary: '#71AEEE',
  primaryForeground: '#FFFFFF',
  secondary: '#71AEEE', // Change secondary to blue too
  secondaryForeground: '#FFFFFF',
  accent: '#71AEEE',
  accentForeground: '#FFFFFF',

  // Success/Complete colors - OVERRIDE GREEN
  success: '#71AEEE', // Override green with blue
  successForeground: '#FFFFFF',

  // Destructive colors - keep as is for errors
  destructive: '#EF4444',
  destructiveForeground: '#FFFFFF',

  // Layout
  radius: '8px',
}

// Function to force iframe sizing
const forceIframeSize = () => {
  const iframe = document.querySelector('.documenso-embed iframe') as HTMLIFrameElement
  if (iframe) {
    console.log('🔧 Forcing iframe size...', {
      currentWidth: iframe.style.width,
      currentHeight: iframe.style.height
    })

    iframe.style.width = '100%'
    iframe.style.maxWidth = '100%'
    iframe.style.height = '1000px'
    iframe.style.minHeight = '800px'
    iframe.style.border = 'none'
    iframe.style.borderRadius = '8px'
    iframe.style.display = 'block'

    // Also set attributes
    iframe.setAttribute('width', '100%')
    iframe.setAttribute('height', '1000')

    console.log('✅ Iframe size forced', {
      newWidth: iframe.style.width,
      newHeight: iframe.style.height
    })
  } else {
    console.log('❌ No iframe found to resize')
  }
}

// Mount hook to force sizing and hide loading after timeout
onMounted(() => {
  // Try to force size after component mounts
  setTimeout(() => forceIframeSize(), 500)
  setTimeout(() => forceIframeSize(), 1000)
  setTimeout(() => forceIframeSize(), 2000)

  // Force hide loading state after 3 seconds if onDocumentReady hasn't fired
  setTimeout(() => {
    if (!documentReady.value) {
      console.log('⚠️ Forcing document ready state after timeout')
      documentReady.value = true
      documentError.value = ''
    }
  }, 3000)

  // Start polling for document completion
  startCompletionPolling()

  // Try to communicate with iframe to force white background
  setTimeout(() => {
    const iframe = document.querySelector('.documenso-embed iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      try {
        // Try to inject styles into iframe
        iframe.contentWindow.postMessage({
          type: 'FORCE_WHITE_BACKGROUND',
          css: `
            body, html, * {
              background-color: white !important;
              background: white !important;
            }
          `
        }, '*')
      } catch (e) {
        console.log('Could not communicate with iframe:', e)
      }
    }
  }, 2000)
})
</script>

<template>
  <div class="documenso-embed">
    <!-- Debug Info -->
    <div v-if="documentError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <h4 class="text-red-800 font-semibold mb-2">Documenso Error:</h4>
      <p class="text-red-700">{{ documentError }}</p>
      <p class="text-sm text-red-600 mt-2">Token: {{ token?.substring(0, 10) }}...</p>
    </div>

    <!-- Loading State -->
    <div v-if="!documentReady && !documentError" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
        <p class="text-blue-700">Loading document for signing...</p>
      </div>
    </div>

    <!-- Success State -->
    <div v-if="documentCompleted" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
      <p class="text-green-700 font-semibold">✅ Document signed successfully!</p>
      <button
        @click="() => emit('documentCompleted', { source: 'manual', timestamp: new Date().toISOString() })"
        class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        🧪 Test Redirect (Manual Trigger)
      </button>
    </div>

    <!-- Manual Test Button (Always Visible for Testing) -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <p class="text-yellow-700 text-sm mb-2">🧪 Testing Tools:</p>
      <button
        @click="() => emit('documentCompleted', { source: 'manual-test', timestamp: new Date().toISOString() })"
        class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
      >
        🚀 Force Redirect Test
      </button>
      <button
        @click="() => { documentCompleted.value = true; console.log('🧪 Manually set documentCompleted to true') }"
        class="ml-2 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 text-sm"
      >
        📝 Set Completed State
      </button>
    </div>

    <!-- Documenso Embed -->
    <EmbedDirectTemplate
      :token="token"
      :name="signerName"
      :email="signerEmail"
      :externalId="externalId"
      :lockName="false"
      :lockEmail="false"
      :css="customCSS"
      :cssVars="cssVariables"
      :darkModeDisabled="true"
      @onDocumentReady="onDocumentReady"
      @onDocumentCompleted="onDocumentCompleted"
      @onDocumentError="onDocumentError"
      @onFieldSigned="onFieldSigned"
      @onFieldUnsigned="onFieldUnsigned"
      @documentCompleted="onDocumentCompleted"
      @document-completed="onDocumentCompleted"
      @completed="onDocumentCompleted"
      @onComplete="onDocumentCompleted"
      @complete="onDocumentCompleted"
    />
  </div>
</template>

<style scoped>
.documenso-embed {
  width: 100%;
  max-width: 100%;
  min-height: 700px;
  height: 1000px;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  border: 1px solid #e5e7eb;
}

/* Force iframe to take full width and height */
:deep(iframe) {
  width: 100% !important;
  max-width: 100% !important;
  min-height: 700px !important;
  height: 1000px !important;
  border: none !important;
  border-radius: 8px !important;
  display: block !important;
}

/* Additional global styles to ensure full width */
:deep(.documenso-embed) {
  width: 100% !important;
  max-width: 100% !important;
  background: white !important;
}

:deep(.documenso-embed > div) {
  width: 100% !important;
  max-width: 100% !important;
  min-height: 700px !important;
  background: white !important;
}

/* Try to force white background on everything */
:deep(*) {
  background-color: white !important;
}

/* Hide loading state more aggressively */
.bg-blue-50 {
  display: none !important;
}

/* Target signature and checkbox fields with blue theme */
:deep(.field--FieldRootContainer[data-field-type='SIGNATURE']) {
  border: 2px solid #71AEEE !important;
  background-color: rgba(113, 174, 238, 0.05) !important;
}

:deep(.field--FieldRootContainer[data-field-type='SIGNATURE'][data-inserted='true']) {
  border: 2px solid #71AEEE !important;
  background-color: rgba(113, 174, 238, 0.2) !important;
}

:deep(.field--FieldRootContainer[data-field-type='CHECKBOX']) {
  border: 2px solid #71AEEE !important;
}

:deep(.field--FieldRootContainer[data-field-type='CHECKBOX'][data-inserted='true']) {
  background-color: #71AEEE !important;
  border: 2px solid #71AEEE !important;
}

/* Additional field targeting */
:deep([data-field-type="SIGNATURE"]) {
  border-color: #71AEEE !important;
  background-color: rgba(113, 174, 238, 0.05) !important;
}

:deep([data-field-type="CHECKBOX"]) {
  accent-color: #71AEEE !important;
}

:deep([data-field-type="CHECKBOX"][data-inserted="true"]) {
  background-color: #71AEEE !important;
}
</style>
